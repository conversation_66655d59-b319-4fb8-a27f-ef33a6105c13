# 独立股票评分程序 - 外部文件版使用说明

## 🎯 程序概述

修改后的 `standalone_stock_scorer.py` 程序已经移除了内置数据，改为读取外部 `list3.csv` 文件。这样的设计允许用户通过简单替换CSV文件来灵活测试不同的股票数据集。

## 📁 文件结构

```
项目目录/
├── standalone_stock_scorer.py    # 主程序文件
├── list3.csv                     # 股票数据文件（用户可替换）
├── corrected_stock_scorer.py     # 备用程序（内置数据版）
└── results_YYYYMMDD_HHMMSS.csv   # 输出结果文件
```

## 📊 输入文件格式

### list3.csv 文件要求

程序要求 `list3.csv` 文件包含以下必要列：

| 列名 | 说明 | 是否必需 |
|------|------|----------|
| `secID` | 股票代码 | ✅ 必需 |
| `名称` | 股票名称 | ⭕ 可选 |
| `closePrice` | 收盘价 | ✅ 必需 |

### 示例文件格式

```csv
secID,名称,closePrice
002636.XSHE,金安国纪,15.09
600475.XSHG,华光环能,16.73
600744.XSHG,华银电力,8.11
605500.XSHG,森林包装,12.07
605162.XSHG,新中港,11.36
```

### 编码支持

程序支持以下文件编码：
- UTF-8 (推荐)
- UTF-8-BOM
- GBK (自动检测)

## 🚀 使用方法

### 方法1: 直接运行（推荐）

```bash
python standalone_stock_scorer.py
```

程序会自动：
1. 检测当前目录下的 `list3.csv` 文件
2. 读取并验证文件格式
3. 对所有股票进行评分
4. 显示评分过程和统计结果
5. 保存结果到 `results_YYYYMMDD_HHMMSS.csv` 文件

### 方法2: 指定输入文件

```bash
python standalone_stock_scorer.py my_stocks.csv
```

### 方法3: 生成详细报告

```bash
python standalone_stock_scorer.py list3.csv --detailed
```

## 📈 程序运行流程

### 1. 文件检测阶段
```
============================================================
独立股票评分程序 - 外部文件读取版
基于原系统算法，确保评分结果完全一致
============================================================
✅ 成功读取文件: list3.csv
📊 加载股票数据，共 20 只股票
包含股票名称信息
```

### 2. 评分处理阶段
```
开始评分...
------------------------------------------------------------
 1. 002636.XSHE 金安国纪   | 评分:  75 | 等级: B+ | 价格:  15.09
 2. 600475.XSHG 华光环能   | 评分:  82 | 等级: A  | 价格:  16.73
 3. 600744.XSHG 华银电力   | 评分:  68 | 等级: B  | 价格:   8.11
...
```

### 3. 统计结果阶段
```
============================================================
📈 评分统计:
  ✅ 成功评分: 20 只
  ❌ 失败: 0 只
  📊 平均评分: 65.2
  📈 评分范围: 45 - 85
  🏆 评级分布:
    A+: 2 只
    A: 3 只
    B+: 5 只
    B: 7 只
    C+: 3 只

💾 结果已保存到: results_20250110_143022.csv
============================================================
```

## 📄 输出文件格式

### results_YYYYMMDD_HHMMSS.csv 包含字段：

| 字段名 | 说明 |
|--------|------|
| 股票代码 | 股票的唯一标识符 |
| 股票名称 | 股票的中文名称 |
| 当前价格 | 收盘价格 |
| 总评分 | 综合评分（0-100分） |
| 评级等级 | 评级等级（A+到F） |
| 趋势评分 | 趋势维度评分（0-30分） |
| 技术指标评分 | 技术指标维度评分（0-25分） |
| 成交量评分 | 成交量维度评分（0-20分） |
| 波动率评分 | 波动率维度评分（0-15分） |
| 动量评分 | 动量维度评分（0-10分） |
| 错误信息 | 如有错误，显示错误信息 |
| 分析时间 | 分析完成时间 |

## 🔄 灵活性特性

### 1. 替换测试数据

用户可以通过以下步骤测试不同的股票组合：

1. **备份原文件**（可选）
   ```bash
   copy list3.csv list3_backup.csv
   ```

2. **准备新数据文件**
   - 确保包含必要的列：`secID`, `closePrice`
   - 可选包含 `名称` 列
   - 保存为 `list3.csv`

3. **运行程序**
   ```bash
   python standalone_stock_scorer.py
   ```

### 2. 处理不同数量的股票

程序可以处理任意数量的股票：
- ✅ 支持1只股票
- ✅ 支持数百只股票
- ✅ 自动适应文件大小

### 3. 错误处理

程序具有完善的错误处理机制：

#### 文件不存在
```
❌ 错误: 未找到文件 'list3.csv'
请确保当前目录下存在 list3.csv 文件
文件应包含以下列: secID, 名称, closePrice

示例文件格式:
secID,名称,closePrice
002636.XSHE,金安国纪,15.09
600475.XSHG,华光环能,16.73
```

#### 文件格式错误
```
❌ 错误: 文件缺少必要的列: ['secID']
当前文件包含的列: ['code', 'name', 'price']
请确保文件包含: secID, closePrice 列
```

#### 编码错误
```
❌ 文件编码错误: 'utf-8' codec can't decode byte...
✅ 成功读取文件: list3.csv (GBK编码)
```

## ⚙️ 评分算法

程序使用与原系统完全一致的五维度评分算法：

### 评分维度和权重
- **趋势分析**: 30% 权重，最高30分
- **技术指标**: 25% 权重，最高25分
- **成交量分析**: 20% 权重，最高20分
- **波动率评估**: 15% 权重，最高15分
- **动量分析**: 10% 权重，最高10分

### 评级等级
- **A+**: 90-100分（优秀）
- **A**: 80-89分（良好）
- **B+**: 70-79分（中上）
- **B**: 60-69分（中等）
- **C+**: 50-59分（中下）
- **C**: 40-49分（较差）
- **D+**: 30-39分（差）
- **D**: 20-29分（很差）
- **F**: 0-19分（极差）

## 🛠️ 故障排除

### 常见问题

1. **程序无法运行**
   - 检查Python环境是否正确安装
   - 确保安装了pandas和numpy库：`pip install pandas numpy`

2. **文件读取失败**
   - 检查文件名是否为 `list3.csv`
   - 确认文件在当前工作目录下
   - 检查文件编码格式

3. **评分结果异常**
   - 确认价格数据为有效数值
   - 检查股票代码格式是否正确

### 技术支持

如遇到问题，请检查：
1. 程序生成的日志文件 `stock_scorer.log`
2. 控制台输出的错误信息
3. 输出CSV文件中的错误信息列

## 📝 总结

修改后的程序实现了以下目标：
- ✅ 移除内置数据，改为读取外部文件
- ✅ 支持灵活的数据替换测试
- ✅ 保持与原系统完全一致的评分算法
- ✅ 提供清晰的错误提示和使用指导
- ✅ 自动生成带时间戳的结果文件
- ✅ 支持任意数量的股票数据处理

通过这种设计，用户可以轻松测试不同的股票组合，而无需修改程序代码，大大提高了程序的实用性和灵活性。
