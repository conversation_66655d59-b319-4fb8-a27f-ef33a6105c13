# 批量股票分析程序使用说明

## 功能概述

本程序可以批量读取CSV文件中的股票代码，自动调用股票分析API，并将分析结果保存为CSV文件。

## 主要功能

1. **股票代码格式转换**
   - `XSHG` → `.SH` (上海证券交易所)
   - `XSHE` → `.SZ` (深圳证券交易所)

2. **批量API调用**
   - 自动调用股票分析API
   - 支持重试机制和错误处理
   - 显示实时进度

3. **数据处理与保存**
   - 提取关键分析指标
   - 保存为结构化CSV文件
   - 生成分析报告

## 文件说明

### 主要程序文件
- `fixed_batch_analyzer.py` - **🔧 修复版主程序** (解决了API连接问题，推荐使用)
- `final_batch_analyzer.py` - 详细版主程序 (包含详细输出)
- `batch_stock_analyzer.py` - 完整版主程序文件 (面向对象设计)
- `run_batch_analysis.py` - 简化版程序文件

### 测试和工具文件
- `test_api_connection.py` - **🔍 API连接测试程序** (推荐先运行)
- `quick_api_test.py` - 快速API测试程序
- `batch_test.py` - 功能测试程序
- `debug_test.py` - 调试测试程序
- `run_analysis.bat` - Windows批处理运行脚本

### 输入输出文件
- `list3.csv` - 输入的股票代码文件
- `批量股票分析使用说明.md` - 本说明文档

## 使用步骤

### 方法一：使用修复版主程序 (推荐)

**🔧 问题已修复：API连接错误已解决**

1. 先测试API连接：
```bash
python test_api_connection.py
```

2. 运行修复版主程序：
```bash
python fixed_batch_analyzer.py
```

或者使用批处理文件 (Windows):
```bash
run_analysis.bat
```

### 方法二：使用完整版程序

运行面向对象设计的完整版程序：

```bash
python batch_stock_analyzer.py
```

### 方法三：测试后运行

1. 首先运行测试程序确保环境正常：

```bash
python batch_test.py
```

2. 确认测试通过后，运行主程序：

```bash
python run_batch_analysis.py
```

### 查看结果

程序运行完成后会生成以下文件：

- `batch_analysis_results_YYYYMMDD_HHMMSS.csv` - 分析结果
- `batch_analysis_failed_YYYYMMDD_HHMMSS.csv` - 失败记录 (如果有)
- `batch_analysis_results_YYYYMMDD_HHMMSS_report.txt` - 分析报告 (完整版)
- `batch_analysis.log` - 详细日志 (完整版)

## 输出字段说明

分析结果CSV文件包含以下字段：

### 基本信息
- `original_code` - 原始股票代码
- `converted_code` - 转换后股票代码
- `stock_name` - 股票名称
- `current_price` - 当前价格
- `change_percent` - 涨跌幅
- `analysis_time` - 分析时间

### 评分信息
- `overall_score` - 综合评分
- `technical_score` - 技术分析评分
- `fundamental_score` - 基本面评分
- `risk_score` - 风险评分

### 风险评估
- `risk_level` - 风险等级
- `volatility` - 波动率
- `beta` - 贝塔系数

### 技术分析
- `trend` - 趋势方向
- `support_level` - 支撑位
- `resistance_level` - 阻力位

### 基本面分析
- `pe_ratio` - 市盈率
- `pb_ratio` - 市净率
- `roe` - 净资产收益率
- `debt_ratio` - 负债率

### AI分析
- `ai_recommendation` - AI推荐
- `ai_confidence` - 置信度
- `ai_summary` - AI总结

## 🔧 问题修复说明

### 已解决的问题

**问题**: `ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接')`

**原因**: API地址中包含了错误的端口号 `:8888`

**解决方案**:
- ❌ 错误地址: `https://fromozu-stock-analysis.hf.space:8888/api/v1/stock/analyze`
- ✅ 正确地址: `https://fromozu-stock-analysis.hf.space/api/v1/stock/analyze`

### 当前API状态

- ✅ **网络连接**: 正常
- ⚠️ **API服务**: 可能存在间歇性500错误（服务端问题）
- ✅ **认证**: API密钥有效

## 配置参数

修复后的正确配置：

```python
API_URL = "https://fromozu-stock-analysis.hf.space/api/v1/stock/analyze"  # 已移除错误端口号
API_KEY = "UZXJfw3YNX80DLfN"
CSV_FILE_PATH = "list3.csv"
```

## 错误处理

程序包含完善的错误处理机制：

1. **网络错误** - 自动重试最多3次
2. **API限流** - 请求间隔0.5秒
3. **超时处理** - 30秒超时限制
4. **数据验证** - 检查股票代码格式

## 注意事项

1. 确保网络连接正常
2. API密钥有效且有足够配额
3. CSV文件格式正确，包含 `secID` 列
4. 程序运行时间取决于股票数量，请耐心等待
5. 建议在非交易时间运行以获得更好的API响应

## 故障排除

### 常见问题及解决方案

1. **🔧 连接错误 (已修复)**
   - ❌ 问题: `ConnectionResetError(10054)`
   - ✅ 解决: 使用修复版程序 `fixed_batch_analyzer.py`
   - 📝 说明: 已移除错误的端口号8888

2. **API服务器错误 (500)**
   - ⚠️ 现象: HTTP 500 Internal Server Error
   - 🔍 原因: API服务端代码bug (`'str' object has no attribute 'get'`)
   - 💡 解决: 这是服务端问题，建议稍后重试或联系服务提供方

3. **CSV文件读取失败**
   - 检查文件路径是否正确
   - 确认文件编码为UTF-8
   - 验证文件包含 `secID` 列

4. **API调用超时**
   - 增加超时时间（已调整为60秒）
   - 检查网络连接稳定性
   - 减少并发请求数量

5. **股票代码格式错误**
   - 检查CSV中的股票代码格式
   - 确认代码以 `.XSHG` 或 `.XSHE` 结尾

### 日志查看

详细的运行日志保存在 `batch_analysis.log` 文件中，可以查看具体的错误信息和处理过程。

## 技术支持

如有问题，请查看日志文件或联系技术支持。
