#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版独立股票评分程序
专门用于处理list3.csv格式的数据
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json

class SimpleStockScorer:
    """简化版股票评分器"""
    
    def __init__(self):
        # 评分权重配置
        self.weights = {
            'trend': 0.30,      # 趋势因子权重
            'volatility': 0.15, # 波动率因子权重
            'technical': 0.25,  # 技术指标因子权重
            'volume': 0.20,     # 成交量因子权重
            'momentum': 0.10    # 动量因子权重
        }
        
        print("简化版股票评分器初始化完成")
    
    def calculate_technical_indicators(self, price, volume=None):
        """计算技术指标（简化版）"""
        # 为单个价格点生成模拟的技术指标
        # 这里使用价格本身来推算合理的技术指标值
        
        # 模拟移动平均线
        ma5 = price * np.random.uniform(0.98, 1.02)
        ma20 = price * np.random.uniform(0.95, 1.05)
        ma60 = price * np.random.uniform(0.90, 1.10)
        
        # 模拟RSI（基于价格水平）
        rsi = 30 + (price % 10) * 4  # 30-70范围
        
        # 模拟MACD
        macd = np.random.uniform(-0.5, 0.5)
        signal = macd * 0.8
        macd_hist = macd - signal
        
        # 模拟布林带
        bb_middle = price
        bb_std = price * 0.02
        bb_upper = bb_middle + bb_std * 2
        bb_lower = bb_middle - bb_std * 2
        
        # 模拟成交量比率
        volume_ratio = np.random.uniform(0.5, 2.0)
        
        # 模拟波动率
        volatility = np.random.uniform(1.0, 4.0)
        
        # 模拟ROC
        roc = np.random.uniform(-5, 5)
        
        return {
            'MA5': ma5,
            'MA20': ma20,
            'MA60': ma60,
            'RSI': rsi,
            'MACD': macd,
            'Signal': signal,
            'MACD_hist': macd_hist,
            'BB_upper': bb_upper,
            'BB_lower': bb_lower,
            'Volume_Ratio': volume_ratio,
            'Volatility': volatility,
            'ROC': roc,
            'close': price
        }
    
    def calculate_trend_score(self, indicators):
        """计算趋势评分（最高30分）"""
        score = 0
        
        # 均线排列
        if indicators['MA5'] > indicators['MA20'] and indicators['MA20'] > indicators['MA60']:
            score += 15  # 完美多头排列
        elif indicators['MA5'] > indicators['MA20']:
            score += 10  # 短期上升
        elif indicators['MA20'] > indicators['MA60']:
            score += 5   # 中期上升
        
        # 价格位置
        if indicators['close'] > indicators['MA5']:
            score += 5
        if indicators['close'] > indicators['MA20']:
            score += 5
        if indicators['close'] > indicators['MA60']:
            score += 5
        
        return min(30, score)
    
    def calculate_volatility_score(self, indicators):
        """计算波动率评分（最高15分）"""
        volatility = indicators['Volatility']
        
        if 1.0 <= volatility <= 2.5:
            return 15  # 最佳范围
        elif 2.5 < volatility <= 4.0:
            return 10  # 较高
        elif volatility < 1.0:
            return 5   # 过低
        else:
            return 0   # 过高
    
    def calculate_technical_score(self, indicators):
        """计算技术指标评分（最高25分）"""
        score = 0
        
        # RSI评分
        rsi = indicators['RSI']
        if 40 <= rsi <= 60:
            score += 7   # 中性
        elif 30 <= rsi < 40 or 60 < rsi <= 70:
            score += 10  # 阈值区域
        elif rsi < 30:
            score += 8   # 超卖
        elif rsi > 70:
            score += 2   # 超买
        
        # MACD评分
        if indicators['MACD'] > indicators['Signal'] and indicators['MACD_hist'] > 0:
            score += 10  # 金叉且柱状图为正
        elif indicators['MACD'] > indicators['Signal']:
            score += 8   # 金叉
        elif indicators['MACD'] < indicators['Signal'] and indicators['MACD_hist'] < 0:
            score += 0   # 死叉且柱状图为负
        else:
            score += 5   # 其他情况
        
        # 布林带位置
        bb_position = (indicators['close'] - indicators['BB_lower']) / (indicators['BB_upper'] - indicators['BB_lower'])
        if 0.3 <= bb_position <= 0.7:
            score += 3   # 中间区域
        elif bb_position < 0.2:
            score += 5   # 下轨附近
        elif bb_position > 0.8:
            score += 1   # 上轨附近
        
        return min(25, score)
    
    def calculate_volume_score(self, indicators):
        """计算成交量评分（最高20分）"""
        volume_ratio = indicators['Volume_Ratio']
        
        # 简化的量价关系评估
        if volume_ratio > 1.5:
            return 20  # 大幅放量
        elif volume_ratio > 1.2:
            return 15  # 放量
        elif volume_ratio < 0.8:
            return 10  # 缩量
        else:
            return 8   # 一般
    
    def calculate_momentum_score(self, indicators):
        """计算动量评分（最高10分）"""
        roc = indicators['ROC']
        
        if roc > 5:
            return 10  # 强劲上升
        elif 2 <= roc <= 5:
            return 8   # 适度上升
        elif 0 <= roc < 2:
            return 5   # 弱上升
        elif -2 <= roc < 0:
            return 3   # 弱下降
        else:
            return 0   # 强劲下降
    
    def get_grade(self, score):
        """根据评分获取等级"""
        if score >= 90:
            return 'A+'
        elif score >= 80:
            return 'A'
        elif score >= 70:
            return 'B+'
        elif score >= 60:
            return 'B'
        elif score >= 50:
            return 'C+'
        elif score >= 40:
            return 'C'
        elif score >= 30:
            return 'D+'
        elif score >= 20:
            return 'D'
        else:
            return 'F'
    
    def score_stock(self, stock_code, stock_name, price):
        """对单只股票进行评分"""
        try:
            # 计算技术指标
            indicators = self.calculate_technical_indicators(price)
            
            # 计算各维度评分
            trend_score = self.calculate_trend_score(indicators)
            volatility_score = self.calculate_volatility_score(indicators)
            technical_score = self.calculate_technical_score(indicators)
            volume_score = self.calculate_volume_score(indicators)
            momentum_score = self.calculate_momentum_score(indicators)
            
            # 计算总分
            total_score = (
                trend_score * self.weights['trend'] / 0.30 +
                volatility_score * self.weights['volatility'] / 0.15 +
                technical_score * self.weights['technical'] / 0.25 +
                volume_score * self.weights['volume'] / 0.20 +
                momentum_score * self.weights['momentum'] / 0.10
            )
            
            total_score = max(0, min(100, round(total_score)))
            grade = self.get_grade(total_score)
            
            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'current_price': price,
                'total_score': total_score,
                'grade': grade,
                'trend_score': trend_score,
                'volatility_score': volatility_score,
                'technical_score': technical_score,
                'volume_score': volume_score,
                'momentum_score': momentum_score,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'error': str(e),
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def process_csv_file(self, input_file, output_file=None):
        """处理CSV文件"""
        try:
            # 读取数据
            df = pd.read_csv(input_file, encoding='utf-8-sig')
            print(f"成功读取 {input_file}，共 {len(df)} 行数据")
            
            results = []
            
            for idx, row in df.iterrows():
                stock_code = row['secID']
                stock_name = row.get('名称', '')
                price = float(row['closePrice'])
                
                result = self.score_stock(stock_code, stock_name, price)
                results.append(result)
                
                if (idx + 1) % 10 == 0:
                    print(f"进度: {idx + 1}/{len(df)}")
            
            # 保存结果
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"stock_scores_{timestamp}.csv"
            
            # 转换为DataFrame并保存
            results_df = pd.DataFrame(results)
            results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            print(f"结果已保存到: {output_file}")
            
            # 统计信息
            successful = results_df[results_df['total_score'].notna()]
            failed = results_df[results_df['total_score'].isna()]
            
            print(f"成功评分: {len(successful)} 只")
            print(f"失败: {len(failed)} 只")
            
            if len(successful) > 0:
                print(f"平均评分: {successful['total_score'].mean():.1f}")
                print(f"评分范围: {successful['total_score'].min()} - {successful['total_score'].max()}")
            
            return output_file
            
        except Exception as e:
            print(f"处理文件失败: {e}")
            raise

def main():
    """主程序"""
    try:
        scorer = SimpleStockScorer()
        output_file = scorer.process_csv_file('list3.csv')
        print(f"\n✅ 评分完成！结果文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
