@echo off
chcp 65001
echo 运行修正版股票评分程序...
echo.

REM 切换到正确的目录
cd /d "d:\Andy\coding\StockAnal_Sys-main"

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 尝试使用py命令...
    py --version >nul 2>&1
    if errorlevel 1 (
        echo 错误: Python未安装或未添加到PATH
        pause
        exit /b 1
    ) else (
        echo 使用py命令运行程序...
        py corrected_stock_scorer.py
    )
) else (
    echo 使用python命令运行程序...
    python corrected_stock_scorer.py
)

echo.
echo 程序执行完成！
pause
