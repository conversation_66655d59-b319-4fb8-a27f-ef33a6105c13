#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的独立股票评分程序
"""

import os
import sys

def test_csv_reading():
    """测试CSV文件读取功能"""
    print("测试修改后的独立股票评分程序")
    print("=" * 50)
    
    # 检查list3.csv文件是否存在
    csv_file = "list3.csv"
    if os.path.exists(csv_file):
        print(f"✅ 找到文件: {csv_file}")
        
        # 读取文件前几行
        try:
            with open(csv_file, 'r', encoding='utf-8-sig') as f:
                lines = f.readlines()[:5]
                print(f"文件包含 {len(lines)} 行（显示前5行）:")
                for i, line in enumerate(lines, 1):
                    print(f"  {i}: {line.strip()}")
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
    else:
        print(f"❌ 未找到文件: {csv_file}")
    
    # 导入并测试修改后的程序
    try:
        print("\n导入standalone_stock_scorer模块...")
        from standalone_stock_scorer import run_csv_analysis
        print("✅ 模块导入成功")
        
        print("\n运行CSV分析功能...")
        result = run_csv_analysis()
        if result:
            print(f"✅ 分析完成，结果文件: {result}")
        else:
            print("❌ 分析失败")
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

if __name__ == "__main__":
    test_csv_reading()
