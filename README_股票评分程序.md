# 独立股票评分程序

## 概述

这是一个基于现有股票分析系统的独立评分程序，能够对任意股票数据进行五维度综合评分。程序提取了完整的评分算法，包括趋势分析、技术指标、成交量分析、波动率评估和动量分析等五个维度。

## 功能特性

### 核心功能
- **五维度评分算法**：完整复制原系统的评分逻辑
- **批量处理**：支持CSV文件批量股票评分
- **详细评分依据**：提供每个维度的具体评分逻辑和计算依据
- **多种输出格式**：支持简要CSV报告和详细文本报告
- **配置化参数**：支持自定义评分参数和权重

### 技术指标计算
- 移动平均线（MA5, MA20, MA60）
- RSI相对强弱指标
- MACD指标及信号线
- 布林带（上轨、中轨、下轨）
- 成交量比率分析
- ATR平均真实波幅
- ROC变动率指标

## 评分维度详解

### 1. 趋势分析（权重30%，最高30分）
- **完美多头排列**（MA5>MA20>MA60）：+15分
- **短期上升趋势**（MA5>MA20）：+10分
- **中期上升趋势**（MA20>MA60）：+5分
- **价格位置**：价格高于各均线分别+5分

### 2. 技术指标（权重25%，最高25分）
- **RSI指标**（10分）：
  - 中性区域（40-60）：+7分
  - 阈值区域（30-40, 60-70）：+10分
  - 超卖区域（<30）：+8分
  - 超买区域（>70）：+2分
- **MACD指标**（10分）：
  - 金叉且柱状图为正：+10分
  - 金叉：+8分
  - 死叉且柱状图为负：+0分
  - 柱状图增长：+5分
- **布林带位置**（5分）：
  - 中间区域（0.3-0.7）：+3分
  - 下轨附近（<0.2）：+5分
  - 上轨附近（>0.8）：+1分

### 3. 成交量分析（权重20%，最高20分）
- **成交量大幅放大且价格上涨**（比率>1.5）：+20分
- **成交量放大且价格上涨**（比率>1.2）：+15分
- **成交量缩减且价格下跌**（比率<0.8）：+10分
- **成交量放大但价格下跌**（比率>1.2）：+0分
- **其他情况**：+8分

### 4. 波动率评估（权重15%，最高15分）
- **最佳波动率范围**（1.0-2.5%）：+15分
- **较高波动率**（2.5-4.0%）：+10分
- **波动率过低**（<1.0%）：+5分
- **波动率过高**（>4.0%）：+0分

### 5. 动量分析（权重10%，最高10分）
- **强劲上升动量**（ROC>5%）：+10分
- **适度上升动量**（ROC 2-5%）：+8分
- **弱上升动量**（ROC 0-2%）：+5分
- **弱下降动量**（ROC -2-0%）：+3分
- **强劲下降动量**（ROC<-2%）：+0分

## 评级等级

| 评分范围 | 等级 | 含义 |
|---------|------|------|
| 90-100  | A+   | 优秀 |
| 80-89   | A    | 良好 |
| 70-79   | B+   | 中上 |
| 60-69   | B    | 中等 |
| 50-59   | C+   | 中下 |
| 40-49   | C    | 较差 |
| 30-39   | D+   | 差 |
| 20-29   | D    | 很差 |
| 0-19    | F    | 极差 |

## 安装和使用

### 环境要求
```bash
pip install pandas numpy
```

### 基本使用
```bash
# 基本评分
python standalone_stock_scorer.py list3.csv

# 指定输出文件
python standalone_stock_scorer.py list3.csv -o my_results.csv

# 生成详细报告
python standalone_stock_scorer.py list3.csv --detailed

# 使用自定义配置
python standalone_stock_scorer.py list3.csv -c scorer_config.json
```

### 输入数据格式

CSV文件必须包含以下列：
- `secID`：股票代码
- `名称`：股票名称（可选）
- `closePrice`：收盘价

程序会自动生成缺失的OHLV数据用于技术指标计算。

### 输出文件

#### 1. 简要CSV报告
包含以下字段：
- 股票代码、股票名称
- 当前价格、总评分、评级等级
- 各维度评分（趋势、技术指标、成交量、波动率、动量）
- 数据点数、分析时间

#### 2. 详细文本报告
包含：
- 完整的评分依据说明
- 各维度的具体指标值
- 详细的评分逻辑解释
- 权重配置信息

## 配置文件

`scorer_config.json` 包含：
- 技术指标计算参数
- 评分权重配置
- 市场类型调整
- 评分阈值设置
- 输出格式配置

## 注意事项

1. **数据质量**：确保输入数据的价格字段为有效数值
2. **数据量**：每只股票至少需要2个数据点进行评分
3. **编码格式**：输出文件使用UTF-8-BOM编码，确保中文正常显示
4. **内存使用**：大批量数据处理时注意内存使用情况

## 版本信息

- **版本**：1.0.0
- **日期**：2025-01-10
- **基于**：股票分析系统评分算法

## 技术支持

如有问题或建议，请查看日志文件 `stock_scorer.log` 获取详细错误信息。
