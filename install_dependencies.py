#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装依赖库脚本
确保独立股票评分程序所需的所有依赖库都已安装
"""

import subprocess
import sys
import importlib

def check_and_install_package(package_name, import_name=None):
    """检查并安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False

def main():
    """主函数"""
    print("=" * 60)
    print("独立股票评分程序 - 依赖库安装检查")
    print("=" * 60)
    
    # 必需的依赖库
    required_packages = [
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("requests", "requests"),
        ("akshare", "akshare")
    ]
    
    print("检查必需的依赖库...")
    
    all_installed = True
    for package_name, import_name in required_packages:
        if not check_and_install_package(package_name, import_name):
            all_installed = False
    
    print("\n" + "=" * 60)
    
    if all_installed:
        print("🎉 所有依赖库已成功安装！")
        print("现在可以运行独立股票评分程序了：")
        print("   python standalone_stock_scorer.py")
    else:
        print("⚠️ 部分依赖库安装失败")
        print("请手动安装失败的库：")
        print("   pip install pandas numpy requests akshare")
    
    print("=" * 60)
    
    # 测试AKShare连接
    try:
        import akshare as ak
        print("\n测试AKShare连接...")
        
        # 尝试获取一个简单的数据
        test_data = ak.stock_zh_a_spot_em()
        if test_data is not None and len(test_data) > 0:
            print("✅ AKShare连接测试成功")
        else:
            print("⚠️ AKShare连接测试返回空数据")
    except Exception as e:
        print(f"⚠️ AKShare连接测试失败: {e}")
        print("这可能是网络问题，程序仍可使用模拟数据运行")

if __name__ == "__main__":
    main()
