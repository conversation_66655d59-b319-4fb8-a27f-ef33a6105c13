# 📋 股票数据预缓存执行指南

## 🚀 快速开始

### 1. 启动系统（自动预缓存）
```bash
# 启动Web服务器，预缓存调度器会自动运行
python web_server.py
```
- ✅ 系统会在每天凌晨 00:00 自动执行预缓存
- ✅ 默认预缓存沪深300前50只成分股数据

### 2. 手动立即执行预缓存
```bash
# 方法一：使用curl命令
curl -X POST http://localhost:8888/api/precache/manual \
  -H "Content-Type: application/json" \
  -d '{"index_code": "000300", "max_stocks": 50}'

# 方法二：直接运行脚本
python stock_precache_scheduler.py
```

## 📊 监控和管理

### 查看预缓存状态
```bash
curl http://localhost:8888/api/precache/status
```

**返回示例：**
```json
{
  "success": true,
  "stats": {
    "last_run": "2025-06-25 00:00:15",
    "total_stocks": 50,
    "success_count": 45,
    "failed_count": 5,
    "duration": 186.5
  },
  "is_running": true
}
```

### 查看执行日志
```bash
# 查看预缓存日志文件
tail -f precache_scheduler.log

# 或者查看Web服务器控制台输出
```

## ⚙️ 配置选项

### 修改执行时间
编辑 `stock_precache_scheduler.py` 文件，找到初始化函数：
```python
def init_precache_scheduler():
    # 修改这里的时间，格式为 "HH:MM"
    precache_scheduler.schedule_daily_precache("02:00", "000300")  # 改为凌晨2点
```

### 修改预缓存股票数量
```bash
# API调用时指定数量
curl -X POST http://localhost:8888/api/precache/manual \
  -H "Content-Type: application/json" \
  -d '{"index_code": "000300", "max_stocks": 100}'  # 缓存100只股票
```

### 选择不同指数
```bash
# 沪深300（默认）
curl -X POST http://localhost:8888/api/precache/manual \
  -d '{"index_code": "000300", "max_stocks": 50}'

# 上证指数
curl -X POST http://localhost:8888/api/precache/manual \
  -d '{"index_code": "000001", "max_stocks": 50}'

# 深证成指
curl -X POST http://localhost:8888/api/precache/manual \
  -d '{"index_code": "399001", "max_stocks": 50}'
```

## 🔧 故障排除

### 常见问题

**1. SSL连接错误**
```
SSLError: EOF occurred in violation of protocol
```
**解决方案：**
- 检查网络连接
- 考虑使用代理或VPN
- 系统会自动重试3次

**2. 数据库表不存在**
```
no such table: stock_basic_info_cache
```
**解决方案：**
```bash
# 重新初始化数据库
python database.py
```

**3. 预缓存任务未执行**
**检查步骤：**
1. 确认Web服务器正在运行
2. 查看控制台是否有错误信息
3. 检查系统时间是否正确

### 手动验证预缓存效果

**1. 运行性能测试：**
```bash
python cache_performance_test.py
```

**2. 运行市场扫描性能对比：**
```bash
python market_scan_performance_test.py
```

## 📈 预期效果

### 性能提升
- **缓存命中时**：3-5秒/股票 → 0.5-1秒/股票
- **整体提升**：60-80%的性能提升
- **用户体验**：几分钟等待 → 1分钟内完成

### 缓存数据类型
- ✅ 股票基本信息（名称、行业、市值等）
- ✅ 历史价格数据（最近1年）
- ✅ 实时数据（当前价格、涨跌幅等）

## 🎯 最佳实践

### 1. 生产环境建议
- 设置在凌晨执行，避免影响白天使用
- 监控预缓存成功率，及时处理失败情况
- 定期清理过期缓存数据

### 2. 开发测试建议
- 使用较少的股票数量进行测试（10-20只）
- 手动执行预缓存验证功能
- 检查缓存数据的完整性

### 3. 网络优化建议
- 在网络稳定的时间段执行
- 考虑使用多个数据源备份
- 设置合理的重试间隔

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件 `precache_scheduler.log`
2. 检查Web服务器控制台输出
3. 运行测试脚本验证功能
4. 确认网络连接和API可用性

---

**注意：** 预缓存功能依赖于外部API，请确保网络连接稳定。首次运行可能需要较长时间，后续执行会更快。
