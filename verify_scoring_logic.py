#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证评分逻辑的正确性
手动测试各个评分维度
"""

def verify_trend_scoring():
    """验证趋势评分逻辑"""
    print("=== 趋势评分验证 ===")
    
    # 测试案例
    test_cases = [
        {
            'name': '完美多头排列',
            'MA5': 20.0, 'MA20': 19.0, 'MA60': 18.0, 'close': 21.0,
            'expected_min': 30  # 15+5+5+5
        },
        {
            'name': '短期上升趋势',
            'MA5': 20.0, 'MA20': 19.0, 'MA60': 19.5, 'close': 20.5,
            'expected_min': 20  # 10+5+5
        },
        {
            'name': '价格低于所有均线',
            'MA5': 20.0, 'MA20': 21.0, 'MA60': 22.0, 'close': 19.0,
            'expected_min': 0
        }
    ]
    
    for case in test_cases:
        score = 0
        
        # 均线排列评分
        if case['MA5'] > case['MA20'] and case['MA20'] > case['MA60']:
            score += 15
        elif case['MA5'] > case['MA20']:
            score += 10
        elif case['MA20'] > case['MA60']:
            score += 5
        
        # 价格位置评分
        if case['close'] > case['MA5']:
            score += 5
        if case['close'] > case['MA20']:
            score += 5
        if case['close'] > case['MA60']:
            score += 5
        
        score = min(30, score)
        
        print(f"  {case['name']}: {score}/30 分")
        print(f"    MA5={case['MA5']}, MA20={case['MA20']}, MA60={case['MA60']}, 价格={case['close']}")
        
        if score >= case['expected_min']:
            print(f"    ✅ 通过 (期望最少{case['expected_min']}分)")
        else:
            print(f"    ❌ 失败 (期望最少{case['expected_min']}分)")
        print()

def verify_technical_scoring():
    """验证技术指标评分逻辑"""
    print("=== 技术指标评分验证 ===")
    
    # RSI测试
    rsi_cases = [
        {'rsi': 50, 'expected': 7, 'desc': 'RSI中性区域'},
        {'rsi': 35, 'expected': 10, 'desc': 'RSI阈值区域'},
        {'rsi': 25, 'expected': 8, 'desc': 'RSI超卖区域'},
        {'rsi': 75, 'expected': 2, 'desc': 'RSI超买区域'}
    ]
    
    for case in rsi_cases:
        rsi = case['rsi']
        if 40 <= rsi <= 60:
            score = 7
        elif 30 <= rsi < 40 or 60 < rsi <= 70:
            score = 10
        elif rsi < 30:
            score = 8
        elif rsi > 70:
            score = 2
        else:
            score = 0
        
        print(f"  {case['desc']} (RSI={rsi}): {score}/10 分", end="")
        if score == case['expected']:
            print(" ✅")
        else:
            print(f" ❌ (期望{case['expected']})")
    
    # MACD测试
    print("\n  MACD测试:")
    macd_cases = [
        {'macd': 0.1, 'signal': 0.05, 'hist': 0.05, 'expected': 10, 'desc': '金叉且柱状图为正'},
        {'macd': 0.1, 'signal': 0.05, 'hist': -0.01, 'expected': 8, 'desc': '金叉'},
        {'macd': -0.1, 'signal': -0.05, 'hist': -0.05, 'expected': 0, 'desc': '死叉且柱状图为负'}
    ]
    
    for case in macd_cases:
        macd, signal, hist = case['macd'], case['signal'], case['hist']
        if macd > signal and hist > 0:
            score = 10
        elif macd > signal:
            score = 8
        elif macd < signal and hist < 0:
            score = 0
        else:
            score = 5
        
        print(f"    {case['desc']}: {score}/10 分", end="")
        if score == case['expected']:
            print(" ✅")
        else:
            print(f" ❌ (期望{case['expected']})")

def verify_volume_scoring():
    """验证成交量评分逻辑"""
    print("\n=== 成交量评分验证 ===")
    
    volume_cases = [
        {'ratio': 1.8, 'price_change': 1.0, 'expected': 20, 'desc': '大幅放量上涨'},
        {'ratio': 1.3, 'price_change': 0.5, 'expected': 15, 'desc': '放量上涨'},
        {'ratio': 0.7, 'price_change': -0.5, 'expected': 10, 'desc': '缩量下跌'},
        {'ratio': 1.5, 'price_change': -0.5, 'expected': 0, 'desc': '放量下跌'},
        {'ratio': 1.0, 'price_change': 0.1, 'expected': 8, 'desc': '一般情况'}
    ]
    
    for case in volume_cases:
        ratio, price_change = case['ratio'], case['price_change']
        
        if ratio > 1.5 and price_change > 0:
            score = 20
        elif ratio > 1.2 and price_change > 0:
            score = 15
        elif ratio < 0.8 and price_change < 0:
            score = 10
        elif ratio > 1.2 and price_change < 0:
            score = 0
        else:
            score = 8
        
        print(f"  {case['desc']} (比率={ratio}, 价格变化={price_change}): {score}/20 分", end="")
        if score == case['expected']:
            print(" ✅")
        else:
            print(f" ❌ (期望{case['expected']})")

def verify_volatility_scoring():
    """验证波动率评分逻辑"""
    print("\n=== 波动率评分验证 ===")
    
    volatility_cases = [
        {'vol': 2.0, 'expected': 15, 'desc': '最佳波动率范围'},
        {'vol': 3.0, 'expected': 10, 'desc': '较高波动率'},
        {'vol': 0.5, 'expected': 5, 'desc': '波动率过低'},
        {'vol': 5.0, 'expected': 0, 'desc': '波动率过高'}
    ]
    
    for case in volatility_cases:
        vol = case['vol']
        
        if 1.0 <= vol <= 2.5:
            score = 15
        elif 2.5 < vol <= 4.0:
            score = 10
        elif vol < 1.0:
            score = 5
        else:
            score = 0
        
        print(f"  {case['desc']} (波动率={vol}%): {score}/15 分", end="")
        if score == case['expected']:
            print(" ✅")
        else:
            print(f" ❌ (期望{case['expected']})")

def verify_momentum_scoring():
    """验证动量评分逻辑"""
    print("\n=== 动量评分验证 ===")
    
    momentum_cases = [
        {'roc': 6.0, 'expected': 10, 'desc': '强劲上升动量'},
        {'roc': 3.0, 'expected': 8, 'desc': '适度上升动量'},
        {'roc': 1.0, 'expected': 5, 'desc': '弱上升动量'},
        {'roc': -1.0, 'expected': 3, 'desc': '弱下降动量'},
        {'roc': -3.0, 'expected': 0, 'desc': '强劲下降动量'}
    ]
    
    for case in momentum_cases:
        roc = case['roc']
        
        if roc > 5:
            score = 10
        elif 2 <= roc <= 5:
            score = 8
        elif 0 <= roc < 2:
            score = 5
        elif -2 <= roc < 0:
            score = 3
        else:
            score = 0
        
        print(f"  {case['desc']} (ROC={roc}%): {score}/10 分", end="")
        if score == case['expected']:
            print(" ✅")
        else:
            print(f" ❌ (期望{case['expected']})")

def verify_total_scoring():
    """验证总分计算逻辑"""
    print("\n=== 总分计算验证 ===")
    
    # 权重配置
    weights = {
        'trend': 0.30,
        'volatility': 0.15,
        'technical': 0.25,
        'volume': 0.20,
        'momentum': 0.10
    }
    
    # 测试案例：各维度满分
    dimension_scores = {
        'trend': 30,
        'volatility': 15,
        'technical': 25,
        'volume': 20,
        'momentum': 10
    }
    
    total_score = (
        dimension_scores['trend'] * weights['trend'] / 0.30 +
        dimension_scores['volatility'] * weights['volatility'] / 0.15 +
        dimension_scores['technical'] * weights['technical'] / 0.25 +
        dimension_scores['volume'] * weights['volume'] / 0.20 +
        dimension_scores['momentum'] * weights['momentum'] / 0.10
    )
    
    total_score = max(0, min(100, round(total_score)))
    
    print(f"  满分测试: {total_score}/100 分", end="")
    if total_score == 100:
        print(" ✅")
    else:
        print(f" ❌ (期望100分)")
    
    # 测试案例：各维度一半分数
    dimension_scores_half = {
        'trend': 15,
        'volatility': 7,
        'technical': 12,
        'volume': 10,
        'momentum': 5
    }
    
    total_score_half = (
        dimension_scores_half['trend'] * weights['trend'] / 0.30 +
        dimension_scores_half['volatility'] * weights['volatility'] / 0.15 +
        dimension_scores_half['technical'] * weights['technical'] / 0.25 +
        dimension_scores_half['volume'] * weights['volume'] / 0.20 +
        dimension_scores_half['momentum'] * weights['momentum'] / 0.10
    )
    
    total_score_half = max(0, min(100, round(total_score_half)))
    
    print(f"  一半分数测试: {total_score_half}/100 分", end="")
    if 45 <= total_score_half <= 55:  # 允许一定误差
        print(" ✅")
    else:
        print(f" ❌ (期望约50分)")

def verify_grade_mapping():
    """验证评级映射"""
    print("\n=== 评级映射验证 ===")
    
    grade_cases = [
        {'score': 95, 'expected': 'A+'},
        {'score': 85, 'expected': 'A'},
        {'score': 75, 'expected': 'B+'},
        {'score': 65, 'expected': 'B'},
        {'score': 55, 'expected': 'C+'},
        {'score': 45, 'expected': 'C'},
        {'score': 35, 'expected': 'D+'},
        {'score': 25, 'expected': 'D'},
        {'score': 15, 'expected': 'F'}
    ]
    
    def get_grade(score):
        if score >= 90:
            return 'A+'
        elif score >= 80:
            return 'A'
        elif score >= 70:
            return 'B+'
        elif score >= 60:
            return 'B'
        elif score >= 50:
            return 'C+'
        elif score >= 40:
            return 'C'
        elif score >= 30:
            return 'D+'
        elif score >= 20:
            return 'D'
        else:
            return 'F'
    
    for case in grade_cases:
        grade = get_grade(case['score'])
        print(f"  评分{case['score']} -> {grade}", end="")
        if grade == case['expected']:
            print(" ✅")
        else:
            print(f" ❌ (期望{case['expected']})")

def main():
    """主验证程序"""
    print("股票评分算法逻辑验证")
    print("=" * 50)
    
    verify_trend_scoring()
    verify_technical_scoring()
    verify_volume_scoring()
    verify_volatility_scoring()
    verify_momentum_scoring()
    verify_total_scoring()
    verify_grade_mapping()
    
    print("\n" + "=" * 50)
    print("验证完成！")

if __name__ == "__main__":
    main()
