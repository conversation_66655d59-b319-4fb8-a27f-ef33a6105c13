#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试独立股票评分程序
"""

import pandas as pd
import numpy as np
from standalone_stock_scorer import StockScorer
import sys

def test_scorer():
    """测试评分程序"""
    try:
        print("开始测试股票评分程序...")
        
        # 创建评分器
        scorer = StockScorer()
        print("✅ 评分器创建成功")
        
        # 加载测试数据
        df = scorer.load_stock_data('list3.csv')
        print(f"✅ 数据加载成功，共 {len(df)} 行")
        
        # 测试单只股票评分
        if len(df) > 0:
            first_stock = df.iloc[0]
            stock_code = first_stock['secID']
            stock_name = first_stock.get('名称', '')
            
            print(f"\n测试股票: {stock_code} ({stock_name})")
            
            # 创建单股票数据
            single_stock_df = pd.DataFrame([first_stock])
            result = scorer.score_single_stock(single_stock_df, stock_code, stock_name)
            
            if 'error' in result:
                print(f"❌ 评分失败: {result['error']}")
            else:
                print(f"✅ 评分成功:")
                print(f"   总评分: {result['total_score']} 分")
                print(f"   评级等级: {result['grade']}")
                print(f"   各维度评分: {result['dimension_scores']}")
        
        # 测试批量评分（前5只股票）
        print(f"\n开始批量评分测试（前5只股票）...")
        test_df = df.head(5)
        
        results = []
        for idx, row in test_df.iterrows():
            stock_code = row['secID']
            stock_name = row.get('名称', '')
            single_stock_df = pd.DataFrame([row])
            result = scorer.score_single_stock(single_stock_df, stock_code, stock_name)
            results.append(result)
        
        # 保存测试结果
        scorer.save_results(results, 'test_results.csv')
        print("✅ 测试结果已保存到 test_results.csv")
        
        # 统计
        successful = [r for r in results if 'total_score' in r]
        failed = [r for r in results if 'error' in r]
        
        print(f"\n测试统计:")
        print(f"  成功: {len(successful)} 只")
        print(f"  失败: {len(failed)} 只")
        
        if successful:
            scores = [r['total_score'] for r in successful]
            print(f"  平均评分: {np.mean(scores):.1f}")
            print(f"  评分范围: {min(scores)} - {max(scores)}")
        
        print("\n✅ 测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_scorer()
    sys.exit(0 if success else 1)
