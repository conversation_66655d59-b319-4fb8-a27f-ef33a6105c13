@echo off
chcp 65001
echo 开始运行股票评分程序...
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请确保已安装Python并添加到PATH环境变量
    pause
    exit /b 1
)

REM 检查必要的库
echo 检查依赖库...
python -c "import pandas, numpy" >nul 2>&1
if errorlevel 1 (
    echo 错误: 缺少必要的库
    echo 正在安装依赖库...
    pip install pandas numpy
)

REM 检查输入文件
if not exist "list3.csv" (
    echo 错误: 未找到输入文件 list3.csv
    pause
    exit /b 1
)

echo.
echo 开始评分...
python simple_stock_scorer.py

echo.
echo 程序执行完成！
pause
